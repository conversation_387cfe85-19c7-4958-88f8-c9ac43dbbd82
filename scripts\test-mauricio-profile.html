<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mauricio Profile Access - Sistema Ministerial</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffe6e6; border-left: 4px solid #ff0000; }
        .success { background: #e6ffe6; border-left: 4px solid #00aa00; }
        .info { background: #e6f3ff; border-left: 4px solid #0066cc; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 300px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🧪 Test <PERSON><PERSON>cio Profile Access - Sistema Ministerial</h1>
    <p>This tool tests if <PERSON><PERSON><PERSON> can access his profile after the RLS policy fixes.</p>
    
    <div class="form-group">
        <label for="email">Email:</label>
        <input type="email" id="email" value="<EMAIL>">
    </div>
    
    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" value="">
    </div>
    
    <div>
        <button onclick="testMauricioLogin()">Test Mauricio Login & Profile Access</button>
        <button onclick="testProfileFetching()">Test Profile Fetching Only</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>
    
    <div id="logs"></div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js';
        
        const SUPABASE_URL = 'https://nwpuurgwnnuejqinkvrh.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im53cHV1cmd3bm51ZWpxaW5rdnJoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0NjIwNjUsImV4cCI6MjA3MDAzODA2NX0.UHjSvXYY_c-_ydAIfELRUs4CMEBLKiztpBGQBNPHfak';
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        const MAURICIO_USER_ID = '5961ba03-bec3-41bd-9fb9-f5e3ef018d2d';
        
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logsDiv.appendChild(logDiv);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        async function testMauricioLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!password) {
                log('❌ Please enter Mauricio\'s password', 'error');
                return;
            }
            
            clearLogs();
            log('🧪 Testing Mauricio\'s login and profile access...', 'info');
            log(`📧 Email: ${email}`, 'info');
            log(`👤 Expected User ID: ${MAURICIO_USER_ID}`, 'info');
            
            try {
                // Step 1: Login
                log('1️⃣ Attempting login...', 'info');
                const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });
                
                if (authError) {
                    log(`❌ Login failed: ${authError.message}`, 'error');
                    return;
                }
                
                log('✅ Login successful!', 'success');
                log(`👤 User ID: ${authData.user.id}`, 'success');
                log(`📧 Email confirmed: ${authData.user.email_confirmed_at ? 'Yes' : 'No'}`, 'info');
                
                // Step 2: Test profile access
                await testProfileAccess(authData.user.id);
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
            }
        }
        
        async function testProfileFetching() {
            clearLogs();
            log('🧪 Testing profile fetching for Mauricio...', 'info');
            
            // Check current auth state
            const { data: { user } } = await supabase.auth.getUser();
            
            if (!user) {
                log('❌ No authenticated user. Please login first.', 'error');
                return;
            }
            
            log(`👤 Current user: ${user.id}`, 'info');
            await testProfileAccess(user.id);
        }
        
        async function testProfileAccess(userId) {
            log('2️⃣ Testing profile access methods...', 'info');
            
            // Method 1: Direct profiles table
            try {
                log('🔄 Method 1: Direct profiles table access...', 'info');
                const { data: profileData, error: profileError } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', userId)
                    .single();
                
                if (profileError) {
                    log(`❌ Method 1 failed: ${profileError.message}`, 'error');
                    log(`   Error code: ${profileError.code}`, 'error');
                    log(`   Error details: ${profileError.details || 'None'}`, 'error');
                } else {
                    log('✅ Method 1 success: Profile found via direct access', 'success');
                    log(`   Name: ${profileData.nome_completo}`, 'success');
                    log(`   Role: ${profileData.role}`, 'success');
                    log(`   Congregation: ${profileData.congregacao}`, 'success');
                    log(`   Birth Date: ${profileData.date_of_birth || 'Not set'}`, 'success');
                }
            } catch (error) {
                log(`❌ Method 1 exception: ${error.message}`, 'error');
            }
            
            // Method 2: User profiles view
            try {
                log('🔄 Method 2: User profiles view...', 'info');
                const { data: viewData, error: viewError } = await supabase
                    .from('user_profiles')
                    .select('*')
                    .eq('id', userId)
                    .single();
                
                if (viewError) {
                    log(`❌ Method 2 failed: ${viewError.message}`, 'error');
                } else {
                    log('✅ Method 2 success: Profile found via view', 'success');
                    log(`   Name: ${viewData.nome_completo}`, 'success');
                    log(`   Email: ${viewData.email}`, 'success');
                    log(`   Role: ${viewData.role}`, 'success');
                }
            } catch (error) {
                log(`❌ Method 2 exception: ${error.message}`, 'error');
            }
            
            // Method 3: Secure function
            try {
                log('🔄 Method 3: Secure function...', 'info');
                const { data: funcData, error: funcError } = await supabase
                    .rpc('get_user_profile', { user_id: userId });
                
                if (funcError) {
                    log(`❌ Method 3 failed: ${funcError.message}`, 'error');
                } else if (funcData && funcData.length > 0) {
                    log('✅ Method 3 success: Profile found via secure function', 'success');
                    log(`   Name: ${funcData[0].nome_completo}`, 'success');
                    log(`   Role: ${funcData[0].role}`, 'success');
                } else {
                    log('⚠️ Method 3: Function returned empty result', 'error');
                }
            } catch (error) {
                log(`❌ Method 3 exception: ${error.message}`, 'error');
            }
            
            // Step 3: Test auth context
            log('3️⃣ Testing auth context...', 'info');
            const { data: { session } } = await supabase.auth.getSession();
            
            if (session) {
                log('✅ Session exists', 'success');
                log(`   User ID: ${session.user.id}`, 'success');
                log(`   Email: ${session.user.email}`, 'success');
                log(`   Role from metadata: ${session.user.user_metadata?.role || 'Not set'}`, 'info');
                log(`   Name from metadata: ${session.user.user_metadata?.nome_completo || 'Not set'}`, 'info');
            } else {
                log('❌ No session found', 'error');
            }
            
            log('🎉 Profile access test completed!', 'success');
        }
        
        // Make functions available globally
        window.testMauricioLogin = testMauricioLogin;
        window.testProfileFetching = testProfileFetching;
        window.clearLogs = clearLogs;
        
        // Initial log
        log('🔧 Mauricio profile access test tool loaded.', 'info');
        log('📋 Enter Mauricio\'s password and click "Test Mauricio Login & Profile Access"', 'info');
        log(`👤 Testing for user ID: ${MAURICIO_USER_ID}`, 'info');
    </script>
</body>
</html>
