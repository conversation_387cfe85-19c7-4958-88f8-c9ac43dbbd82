{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "skipLibCheck": true}, "include": ["src"], "exclude": ["node_modules", "dist"]}