{"compilerOptions": {"target": "es5", "lib": ["es5", "dom"], "types": ["cypress", "node"], "strict": false, "noImplicitAny": false, "strictNullChecks": false, "noUnusedLocals": false, "noUnusedParameters": false, "skipLibCheck": true, "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "baseUrl": "..", "paths": {"@/*": ["./src/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules"]}