@tailwind base;
@tailwind components;
@tailwind utilities;

/* ag-grid custom styles */
.ag-theme-alpine {
  --ag-header-background-color: #f8fafc;
  --ag-header-foreground-color: #1e293b;
  --ag-border-color: #e2e8f0;
  --ag-row-hover-color: #f1f5f9;
  --ag-selected-row-background-color: #dbeafe;
  --ag-cell-horizontal-border: solid #e2e8f0;
  --ag-cell-vertical-border: solid #e2e8f0;
  --ag-row-height: 40px;
  --ag-header-height: 48px;
}

.ag-theme-alpine .ag-header-cell {
  font-weight: 600;
}

.ag-theme-alpine .ag-cell {
  padding: 8px 12px;
  line-height: 1.3;
}

.ag-theme-alpine .ag-cell-edit-input {
  padding: 4px 8px;
  border: 1px solid #3b82f6;
  border-radius: 4px;
}

.ag-theme-alpine .ag-cell-inline-editing {
  background-color: #eff6ff;
}

.ag-theme-alpine .ag-row {
  border-bottom: 1px solid #e2e8f0;
  min-height: 40px;
}

.ag-theme-alpine .ag-cell-wrap-text {
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.4;
}

.ag-theme-alpine .ag-row-auto-height .ag-cell {
  padding: 8px 12px;
  align-items: center;
  display: flex;
}

.ag-theme-alpine .ag-cell-value {
  line-height: 1.4;
  word-break: break-word;
}

.ag-theme-alpine .ag-header-group-cell {
  background-color: #f3f4f6;
  font-weight: 700;
  color: #1f2937;
  border-bottom: 2px solid #d1d5db;
  text-align: center;
}

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 208 100% 23%;
    --primary-foreground: 0 0% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 208 100% 23%;

    /* Custom tokens for JW-inspired design */
    --jw-blue: 208 100% 23%;
    --jw-blue-light: 208 100% 35%;
    --jw-blue-dark: 208 100% 15%;
    --jw-gold: 43 100% 50%;
    --jw-navy: 217 33% 17%;
    
    /* Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(var(--jw-blue)), hsl(var(--jw-blue-dark)));
    --gradient-card: linear-gradient(180deg, hsl(var(--background)), hsl(var(--muted)));
    
    /* Shadows */
    --shadow-soft: 0 4px 20px hsla(var(--jw-blue), 0.15);
    --shadow-strong: 0 8px 30px hsla(var(--jw-blue), 0.25);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
    box-sizing: border-box;
  }

  body {
    @apply bg-background text-foreground;
  }
  
  /* Prevent horizontal scroll on all elements */
  *, *::before, *::after {
    max-width: 100%;
  }
}

/* Global overflow prevention */
html, body {
  overflow-x: hidden;
}

/* Tablet Portrait Responsive Fixes */
@media (max-width: 1024px) and (orientation: portrait) {
  .container {
    @apply px-2 sm:px-4;
  }
  
  /* Header adjustments */
  .header-nav {
    @apply hidden;
  }
  
  .header-mobile-nav {
    @apply flex;
  }
  
  /* Card grids responsive */
  .students-grid {
    @apply grid-cols-1 sm:grid-cols-2;
  }
  
  /* Tab navigation */
  .tabs-list {
    @apply grid-cols-3 sm:grid-cols-6;
  }
  
  /* Form layouts */
  .form-grid {
    @apply grid-cols-1 sm:grid-cols-2;
  }
  
  /* Modal adjustments */
  .modal-content {
    @apply max-w-[90vw] max-h-[80vh] overflow-y-auto;
  }
}

/* Mobile Portrait Specific */
@media (max-width: 768px) and (orientation: portrait) {
  .container {
    @apply px-2;
  }
  
  .students-grid {
    @apply grid-cols-1;
  }
  
  .tabs-list {
    @apply grid-cols-2;
  }
  
  .form-grid {
    @apply grid-cols-1;
  }
  
  /* Header title responsive */
  .header-title {
    @apply text-lg sm:text-xl;
  }
  
  /* Button groups */
  .button-group {
    @apply flex-col sm:flex-row gap-2;
  }
}

/* AG Grid responsive adjustments */
@media (max-width: 1024px) {
  .ag-theme-alpine {
    --ag-row-height: 36px;
    --ag-header-height: 44px;
  }
  
  .ag-theme-alpine .ag-cell {
    padding: 6px 8px;
    font-size: 14px;
  }
  
  .ag-theme-alpine .ag-header-cell {
    font-size: 13px;
  }
}

/* Utility classes for responsive design */
.responsive-container {
  @apply w-full max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8 overflow-x-hidden;
}

.responsive-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 max-w-full;
}

.responsive-tabs {
  @apply grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6;
}

.responsive-form {
  @apply grid grid-cols-1 sm:grid-cols-2 gap-4;
}

.responsive-buttons {
  @apply flex flex-col sm:flex-row gap-2 sm:gap-4;
}

/* CTA flutuante fixo */
.floating-cta {
  @apply fixed right-4 bottom-6 z-40 rounded-full shadow-lg px-4 py-3 text-sm font-semibold bg-primary text-white max-w-[90vw] md:right-6 md:bottom-8;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* utilitário simples */
.scrollbar-none::-webkit-scrollbar { display: none; }
@supports (text-wrap: balance) {
  .text-balance { text-wrap: balance; }
}

/* Clamp utility for responsive text */
.text-clamp {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@layer base {
  html, body, #root {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    min-height: 100dvh;
  }

  body {
    padding-bottom: env(safe-area-inset-bottom);
  }

  img, video, canvas, svg {
    max-width: 100%;
    height: auto;
  }

  #root {
    max-width: 100% !important;
    padding: 0 !important;
  }

  /* Safe area support */
  :root {
    --safe-top: env(safe-area-inset-top, 0px);
    --safe-bottom: env(safe-area-inset-bottom, 0px);
    --safe-left: env(safe-area-inset-left, 0px);
    --safe-right: env(safe-area-inset-right, 0px);
  }

  .safe-top {
    padding-top: var(--safe-top);
  }

  .safe-bottom {
    padding-bottom: var(--safe-bottom);
  }

  .min-h-svh {
    min-height: 100svh;
  }
}