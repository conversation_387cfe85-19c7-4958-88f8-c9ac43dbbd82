# Configuração de Testes Cypress - Sistema Ministerial

## 🎯 Objetivo

Implementação completa de testes automatizados E2E usando Cypress para testar o login do estudante Franklin e navegação no Sistema Ministerial.

## ✅ Implementação Completa

### 📁 Estrutura de Arquivos Criada

```
cypress/
├── e2e/
│   ├── franklin-login.cy.ts           # Teste principal do login
│   ├── student-portal-navigation.cy.ts # Navegação no portal
│   └── url-configuration.cy.ts        # Configuração de URLs
├── fixtures/
│   └── franklin-user.json             # Dados de teste
├── support/
│   ├── commands.ts                     # Comandos customizados
│   ├── component.ts                    # Suporte para testes de componente
│   └── e2e.ts                         # Configurações globais
└── README.md                          # Documentação completa

cypress.config.ts                      # Configuração principal
scripts/test-franklin-cypress.js       # Script de execução
```

### 🔧 Configuração Implementada

#### 1. **Cypress Config** (`cypress.config.ts`)
- ✅ Base URL: `https://sua-parte.lovable.app`
- ✅ Credenciais do Franklin configuradas
- ✅ Timeouts otimizados para aplicação
- ✅ Configurações de retry e video

#### 2. **Comandos Customizados** (`cypress/support/commands.ts`)
- ✅ `cy.loginAsFranklin()` - Login automático
- ✅ `cy.waitForPageLoad()` - Aguardar carregamento
- ✅ `cy.shouldBeOnPage(path)` - Verificar página
- ✅ `cy.waitForElement(selector)` - Aguardar elemento

#### 3. **Scripts NPM** (package.json)
```json
{
  "cypress:open": "cypress open",
  "cypress:run": "cypress run", 
  "test:e2e": "cypress run",
  "test:e2e:open": "cypress open",
  "test:franklin": "cypress run --spec 'cypress/e2e/franklin-login.cy.ts'"
}
```

## 🧪 Testes Implementados

### 1. **franklin-login.cy.ts** - Teste Principal
```typescript
✅ Login com sucesso e redirecionamento para portal
✅ Persistência de sessão após refresh  
✅ Tratamento de credenciais inválidas
✅ Funcionalidade de logout
```

### 2. **student-portal-navigation.cy.ts** - Portal do Estudante
```typescript
✅ Exibição de informações pessoais
✅ Seções da Escola do Ministério
✅ Responsividade em diferentes telas
✅ Verificação de erros de console
✅ Persistência de dados após navegação
```

### 3. **url-configuration.cy.ts** - Configuração de URLs
```typescript
✅ Redirecionamento de usuário não autenticado
✅ Bloqueio de acesso a portal de outro usuário
✅ Navegação entre páginas públicas
✅ Manutenção de URL durante login
✅ Tratamento de URLs inválidas
```

## 🚀 Como Executar

### Instalação
```bash
# Instalar dependências (Cypress já incluído)
npm install
```

### Execução dos Testes

#### Modo Interativo (Recomendado)
```bash
# Abrir interface do Cypress
npm run cypress:open

# Ou usar script customizado
node scripts/test-franklin-cypress.js --open
```

#### Modo Headless (Automático)
```bash
# Executar todos os testes
npm run test:e2e

# Executar apenas teste do Franklin
npm run test:franklin

# Ou usar script customizado
node scripts/test-franklin-cypress.js
```

#### Testes Específicos
```bash
# Apenas login do Franklin
npx cypress run --spec "cypress/e2e/franklin-login.cy.ts"

# Apenas navegação no portal
npx cypress run --spec "cypress/e2e/student-portal-navigation.cy.ts"

# Apenas configuração de URLs
npx cypress run --spec "cypress/e2e/url-configuration.cy.ts"
```

## 📋 Credenciais de Teste

### Franklin (Estudante)
```
Email: <EMAIL>
Senha: 13a21r15
User ID: 77c99e53-500b-4140-b7fc-a69f96b216e1
Portal: /estudante/77c99e53-500b-4140-b7fc-a69f96b216e1
```

## 🎯 Cenários de Teste Cobertos

### ✅ Happy Path (Caminho Feliz)
1. **Login bem-sucedido**
   - Preencher credenciais corretas
   - Submeter formulário
   - Aguardar autenticação
   - Verificar redirecionamento
   - Confirmar carregamento do portal

2. **Navegação no portal**
   - Verificar dados pessoais
   - Verificar seções do ministério
   - Testar responsividade
   - Verificar persistência de dados

### ✅ Error Path (Tratamento de Erros)
1. **Credenciais inválidas**
   - Tentar login com senha incorreta
   - Verificar mensagem de erro
   - Confirmar que não foi redirecionado

2. **Acesso não autorizado**
   - Tentar acessar portal sem login
   - Tentar acessar portal de outro usuário
   - Verificar redirecionamentos de segurança

### ✅ Edge Cases (Casos Extremos)
1. **Persistência de sessão**
   - Refresh da página
   - Navegação com botões do browser
   - URLs inválidas

2. **Responsividade**
   - Diferentes tamanhos de tela
   - Mobile, tablet, desktop

## 📊 Relatórios e Debug

### Arquivos Gerados
- **Vídeos**: `cypress/videos/` - Gravação de todos os testes
- **Screenshots**: `cypress/screenshots/` - Capturas em caso de falha
- **Logs**: Console detalhado com emojis para fácil identificação

### Exemplo de Log
```
🎯 Teste: Login do Franklin e redirecionamento para portal do estudante
📍 Passo 1: Navegando para página de login
📝 Passo 2: Preenchendo credenciais do Franklin
🚀 Passo 3: Submetendo formulário de login
⏳ Passo 4: Aguardando resposta da autenticação
✅ Autenticação bem-sucedida
🔄 Passo 5: Verificando redirecionamento para portal do estudante
📋 Passo 6: Verificando carregamento do portal do estudante
👤 Passo 7: Verificando dados do usuário
🎉 Teste concluído com sucesso!
```

## 🔧 Configurações Avançadas

### Interceptações de Rede
```typescript
cy.intercept('POST', '**/auth/v1/token**').as('authLogin')
cy.intercept('GET', '**/rest/v1/user_profiles**').as('getUserProfile')
```

### Timeouts Configurados
- **Page Load**: 30 segundos
- **Element Visible**: 10 segundos  
- **Auth Response**: 20 segundos
- **Redirect**: 30 segundos

### Retry Configuration
- **Run Mode**: 2 tentativas
- **Open Mode**: 0 tentativas (para debug)

## 🚨 Troubleshooting

### Problemas Comuns

#### 1. Teste falha no login
```bash
# Verificações:
- Aplicação está rodando em https://sua-parte.lovable.app
- Credenciais estão corretas
- Conectividade de rede está OK
- Routing fixes estão aplicados
```

#### 2. Timeout em redirecionamento
```bash
# Soluções:
- Verificar console do browser para erros
- Aumentar timeout se necessário
- Verificar se profile fetching está funcionando
- Testar manualmente o fluxo
```

#### 3. Elementos não encontrados
```bash
# Soluções:
- Verificar seletores CSS
- Aguardar carregamento com cy.waitForElement()
- Verificar se componente está renderizando
- Usar modo interativo para debug
```

## 📈 Próximos Passos

### Melhorias Futuras
- [ ] Testes para outros tipos de usuário (instrutor)
- [ ] Testes de funcionalidades específicas do portal
- [ ] Testes de performance e acessibilidade
- [ ] Integração com CI/CD pipeline
- [ ] Testes de API endpoints
- [ ] Testes de componentes React

### Integração CI/CD
```yaml
# Exemplo para GitHub Actions
- name: Run Cypress Tests
  run: npm run test:e2e
```

## 🎉 Resultado Esperado

Após executar os testes, você deve ver:

```
✅ franklin-login.cy.ts - 4 testes passando
✅ student-portal-navigation.cy.ts - 6 testes passando  
✅ url-configuration.cy.ts - 8 testes passando

Total: 18 testes passando
Tempo: ~2-3 minutos
```

**Status**: ✅ **IMPLEMENTAÇÃO COMPLETA**  
**Pronto para**: ✅ **EXECUÇÃO IMEDIATA**  
**Cobertura**: ✅ **CENÁRIOS CRÍTICOS COBERTOS**
