{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:all": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "vite", "dev:backend-only": "cd backend && npm run dev", "dev:frontend-only": "vite", "build": "vite build", "build:prod": "vite build --config vite.config.prod.ts", "build:analyze": "vite build --config vite.config.prod.ts && npm run analyze", "analyze": "npx vite-bundle-analyzer dist", "build:dev": "vite build --mode development", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "typecheck": "tsc -p tsconfig.json", "preview": "vite preview", "start": "serve -s dist -l $PORT", "cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:install": "cypress install", "cypress:verify": "cypress verify", "test:e2e": "cypress run --spec \"cypress/e2e/**/*.cy.ts\"", "test:e2e:open": "cypress open", "test:e2e:record": "cypress run --record", "test:franklin": "cypress run --spec \"cypress/e2e/franklin-login.cy.ts\"", "test:franklin:record": "cypress run --spec \"cypress/e2e/franklin-login.cy.ts\" --record", "test:sarah": "cypress run --spec \"cypress/e2e/sarah-student-registration.cy.ts\"", "test:sarah:record": "cypress run --spec \"cypress/e2e/sarah-student-registration.cy.ts\" --record", "test:birth-date": "cypress run --spec \"cypress/e2e/sarah-student-registration.cy.ts\"", "test:sarah:open": "cypress open --e2e --browser chrome", "test:audit": "cypress run --spec \"cypress/e2e/auditoria_sistema_ministerial.cy.ts\"", "test:audit:record": "cypress run --spec \"cypress/e2e/auditoria_sistema_ministerial.cy.ts\" --record", "test:auth": "cypress run --spec \"cypress/e2e/authentication-roles.cy.ts\"", "test:auth:record": "cypress run --spec \"cypress/e2e/authentication-roles.cy.ts\" --record", "test:pdf-upload": "cypress run --spec \"cypress/e2e/pdf-upload-functionality.cy.ts\"", "test:pdf-upload:record": "cypress run --spec \"cypress/e2e/pdf-upload-functionality.cy.ts\" --record", "env:validate": "node scripts/validate-env.js", "env:check": "node scripts/validate-env.js", "env:show": "node -e \"console.log('Environment Variables:'); Object.keys(process.env).filter(k => k.startsWith('VITE_') || ['NODE_ENV', 'DATABASE_URL'].includes(k)).forEach(k => console.log(k + '=' + (k.includes('SECRET') || k.includes('TOKEN') || k.includes('KEY') ? '[HIDDEN]' : process.env[k])))\"", "test:programs": "cypress run --spec \"cypress/e2e/programs-page-functionality.cy.ts\"", "test:programs:record": "cypress run --spec \"cypress/e2e/programs-page-functionality.cy.ts\" --record", "test:sistema-completo": "cypress run --spec \"cypress/e2e/sistema-ministerial-e2e.cy.ts\"", "test:sistema-completo:record": "cypress run --spec \"cypress/e2e/sistema-ministerial-e2e.cy.ts\" --record", "test:enhanced-parsing": "cypress run --spec \"cypress/e2e/enhanced-pdf-parsing.cy.ts\"", "test:enhanced-parsing:record": "cypress run --spec \"cypress/e2e/enhanced-pdf-parsing.cy.ts\" --record", "test:assignment-generation": "cypress run --spec \"cypress/e2e/assignment-generation.cy.ts\"", "test:assignment-generation:record": "cypress run --spec \"cypress/e2e/assignment-generation.cy.ts\" --record", "test:all-new": "cypress run --spec \"cypress/e2e/{pdf-upload-functionality,programs-page-functionality,sistema-ministerial-e2e,enhanced-pdf-parsing,assignment-generation}.cy.ts\"", "test:complete-workflow": "cypress run --spec \"cypress/e2e/{enhanced-pdf-parsing,assignment-generation}.cy.ts\"", "test:integration": "node src/verification/integration-test-cli.js", "test:integration:performance": "node -e \"import('./src/verification/tests/integration-test-runner.js').then(m => m.runIntegrationTestSuite())\"", "test:integration:services": "node -e \"console.log('🔗 Running service integration tests...'); import('./src/verification/tests/integration-test-runner.js').then(m => m.runIntegrationTestSuite())\"", "verify:integration": "node src/verification/test-integration-simple.js", "test:setup": "cypress install && cypress verify", "test:verification": "node src/verification/tests/test-runner.cjs", "test:verification:simple": "node src/verification/tests/test-runner.cjs --simple", "test:verification:basic": "node src/verification/tests/test-runner.cjs --basic", "audit:plan": "node ./scripts/audit-implementation.mjs", "verify:system": "node src/verification/cli.js --full", "verify:frontend": "node src/verification/cli.js --module=frontend", "verify:backend": "node src/verification/cli.js --module=backend", "verify:infrastructure": "node src/verification/cli.js --module=infrastructure", "verify:reports": "node src/verification/cli.js reports", "verify:reports:list": "node src/verification/cli.js reports list", "verify:reports:dashboard": "node src/verification/cli.js reports dashboard", "verify:reports:trends": "node src/verification/cli.js reports trends", "verify:reports:cleanup": "node src/verification/cli.js reports cleanup", "verify:reports:init": "node src/verification/cli.js reports init", "test:report-storage": "node src/verification/test-report-storage.js", "test:frontend-verifier": "node src/verification/test-frontend-verifier.js", "test": "cypress run", "test:open": "cypress open", "test:component": "cypress run --component", "test:performance": "lighthouse http://localhost:8080/admin --output html --output-path ./lighthouse-report.html", "optimize": "npm run build:prod && npm run analyze"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/mcp-server-supabase": "^0.4.5", "@supabase/supabase-js": "^2.54.0", "@tanstack/react-query": "^5.83.0", "ag-grid-community": "^34.1.1", "ag-grid-react": "^34.1.1", "cheerio": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "dotenv": "^17.2.1", "embla-carousel-react": "^8.6.0", "fs-extra": "^11.3.1", "i18next": "^25.3.6", "i18next-browser-languagedetector": "^8.2.0", "input-otp": "^1.4.2", "jspdf": "^3.0.1", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "node-fetch": "^3.3.2", "pdf-parse": "^1.1.1", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.61.1", "react-i18next": "^15.6.1", "react-resizable-panels": "^2.1.9", "react-router-dom": "^6.30.1", "recharts": "^2.15.4", "serve": "^14.2.3", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.9", "xlsx": "^0.18.5", "zod": "^3.25.76"}, "overrides": {"rollup": "^4.0.0"}, "devDependencies": {"@eslint/js": "^9.32.0", "@tailwindcss/typography": "^0.5.16", "@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.27.0", "@types/babel__template": "^7.4.4", "@types/node": "^22.17.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "cssnano": "^7.1.0", "cypress": "^13.17.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "fast-glob": "^3.3.3", "globals": "^15.15.0", "lovable-tagger": "^1.1.8", "postcss": "^8.5.6", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0", "vite": "^5.0.0", "vite-plugin-pwa": "^1.0.3"}}