{"root": ["./src/app.tsx", "./src/i18n.ts", "./src/main.tsx", "./src/sw-register.ts", "./src/translations.ts", "./src/vite-env.d.ts", "./src/app/estudantes/page.tsx", "./src/app/programas/layout.tsx", "./src/app/programas/page.tsx", "./src/components/assignmenteditmodal.tsx", "./src/components/assignmentgenerationmodal.tsx", "./src/components/assignmentpreviewmodal.tsx", "./src/components/assignmentstatuscard.tsx", "./src/components/benefits.tsx", "./src/components/debugfab.tsx", "./src/components/debugpanel.tsx", "./src/components/debugpaneltest.tsx", "./src/components/donationcard.tsx", "./src/components/estudantecard.tsx", "./src/components/estudanteform.tsx", "./src/components/faqsection.tsx", "./src/components/fallbackscreen.tsx", "./src/components/familyinvitationdebugpanel.tsx", "./src/components/familymemberform.tsx", "./src/components/familymemberslist.tsx", "./src/components/features.tsx", "./src/components/footer.tsx", "./src/components/header.tsx", "./src/components/hero.tsx", "./src/components/importhelp.tsx", "./src/components/jwcontentparser.tsx", "./src/components/jwterminologyhelper.tsx", "./src/components/landinghero.tsx", "./src/components/languagedebug.tsx", "./src/components/meetingmanagement.tsx", "./src/components/modalpreviadesignacoes.tsx", "./src/components/modalselecaosemana.tsx", "./src/components/pdftestbutton.tsx", "./src/components/pdfparsingdemo.tsx", "./src/components/pdfupload.tsx", "./src/components/productiondebugpanel.tsx", "./src/components/programdetailmodal.tsx", "./src/components/protectedroute.tsx", "./src/components/quickactions.tsx", "./src/components/spreadsheetupload.tsx", "./src/components/studentassignmentview.tsx", "./src/components/studentsspreadsheet.tsx", "./src/components/templatedownload.tsx", "./src/components/templatelibrary.tsx", "./src/components/tutorialintegration.tsx", "./src/components/tutorialmanager.tsx", "./src/components/assignments/designacoestoolbar.tsx", "./src/components/auth/loginfeedback.tsx", "./src/components/auth/simpleloginfeedback.tsx", "./src/components/examples/intelligenttoolbarexample.tsx", "./src/components/examples/responsivetableexample.tsx", "./src/components/instructor/instructordashboardstats.tsx", "./src/components/instructor/pendingstudentspanel.tsx", "./src/components/instructor/progressboard.tsx", "./src/components/instructor/speechtypecategories.tsx", "./src/components/instructor/studentqualificationcard.tsx", "./src/components/instructor/index.ts", "./src/components/layout/intelligenttoolbar.tsx", "./src/components/layout/pageshell.tsx", "./src/components/layout/responsivetablewrapper.tsx", "./src/components/layout/adaptive-grid.tsx", "./src/components/layout/mobile-tabs.tsx", "./src/components/layout/quick-actions.tsx", "./src/components/layout/responsive-container.tsx", "./src/components/layout/responsive-header.tsx", "./src/components/layout/responsive-integration.tsx", "./src/components/migration/familymigrationpanel.tsx", "./src/components/navigation/mobilenavigation.tsx", "./src/components/navigation/programasnavbutton.tsx", "./src/components/programs/programastoolbar.tsx", "./src/components/students/estudantestoolbar.tsx", "./src/components/students/studentspreadsheetoptimized.tsx", "./src/components/students/studentsgrid.tsx", "./src/components/students/studentsgridag.tsx", "./src/components/students/studentspagewithmodes.tsx", "./src/components/students/studentsviewtoggle.tsx", "./src/components/tests/densitytoggletest.tsx", "./src/components/tests/intelligenttoolbartest.tsx", "./src/components/tests/responsiveintegrationtest.tsx", "./src/components/tests/responsivetablewrappertest.tsx", "./src/components/tests/tailwindbreakpointtest.tsx", "./src/components/tests/zoomresponsivenesstest.tsx", "./src/components/tutorial/tutorialbutton.tsx", "./src/components/tutorial/tutorialoverlay.tsx", "./src/components/tutorial/tutorialtooltip.tsx", "./src/components/tutorial/index.ts", "./src/components/ui/accordion.tsx", "./src/components/ui/alert-dialog.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/aspect-ratio.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/breadcrumb.tsx", "./src/components/ui/button.tsx", "./src/components/ui/calendar.tsx", "./src/components/ui/card.tsx", "./src/components/ui/carousel.tsx", "./src/components/ui/chart.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/collapsible.tsx", "./src/components/ui/command.tsx", "./src/components/ui/context-menu.tsx", "./src/components/ui/density-toggle.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/drawer.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/empty-state.tsx", "./src/components/ui/enhanced-responsive-table.tsx", "./src/components/ui/form.tsx", "./src/components/ui/hover-card.tsx", "./src/components/ui/input-otp.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/menubar.tsx", "./src/components/ui/navigation-menu.tsx", "./src/components/ui/pagination.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radio-group.tsx", "./src/components/ui/resizable.tsx", "./src/components/ui/responsive-container.tsx", "./src/components/ui/responsive-table.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/scroll-tabs.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/sheet.tsx", "./src/components/ui/sidebar.tsx", "./src/components/ui/skeleton-list.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/sonner.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/components/ui/toggle-group.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/components/ui/use-toast.ts", "./src/config/donations.ts", "./src/config/tutorials.ts", "./src/contexts/authcontext.tsx", "./src/contexts/authcontextfallback.tsx", "./src/contexts/densitycontext.tsx", "./src/contexts/languagecontext.tsx", "./src/contexts/tutorialcontext.tsx", "./src/hooks/use-mobile.tsx", "./src/hooks/use-responsive-table.ts", "./src/hooks/use-responsive.ts", "./src/hooks/use-timeout-fetch.ts", "./src/hooks/use-toast.ts", "./src/hooks/useassignmentgeneration.ts", "./src/hooks/useenhancedestudantes.ts", "./src/hooks/useenhancedfamilymigration.ts", "./src/hooks/useestudantes.ts", "./src/hooks/usefamilymembers.ts", "./src/hooks/useinstructordashboard.ts", "./src/hooks/usemeetings.ts", "./src/hooks/usenotifications.ts", "./src/hooks/usepdfupload.ts", "./src/hooks/useprofileloader.ts", "./src/hooks/useresponsive.ts", "./src/hooks/usespreadsheetimport.ts", "./src/hooks/usestudentsgrid.ts", "./src/hooks/usetranslation.ts", "./src/hooks/useviewmode.ts", "./src/integrations/supabase/client.ts", "./src/integrations/supabase/enhanced-client.ts", "./src/integrations/supabase/types.ts", "./src/layouts/safearealayout.tsx", "./src/lib/supabase.ts", "./src/lib/utils.ts", "./src/pages/admindashboard.tsx", "./src/pages/auth.tsx", "./src/pages/bemvindo.tsx", "./src/pages/configuracaoinicial.tsx", "./src/pages/congregacoes.tsx", "./src/pages/dashboard.tsx", "./src/pages/demo.tsx", "./src/pages/densitytoggletest.tsx", "./src/pages/designacoes.tsx", "./src/pages/designacoesoptimized.tsx", "./src/pages/developerpanel.tsx", "./src/pages/doar.tsx", "./src/pages/equidade.tsx", "./src/pages/estudanteportal.tsx", "./src/pages/estudantes.tsx", "./src/pages/estudantesoptimized.tsx", "./src/pages/estudantesresponsive.tsx", "./src/pages/funcionalidades.tsx", "./src/pages/index.tsx", "./src/pages/intelligenttoolbartest.tsx", "./src/pages/notfound.tsx", "./src/pages/pdfparsingtest.tsx", "./src/pages/portalfamiliar.tsx", "./src/pages/primeiroprograma.tsx", "./src/pages/programapreview.tsx", "./src/pages/programas.tsx", "./src/pages/programasoptimized.tsx", "./src/pages/programastest.tsx", "./src/pages/relatorios.tsx", "./src/pages/responsiveintegrationtestpage.tsx", "./src/pages/reunioes.tsx", "./src/pages/sobre.tsx", "./src/pages/studentdashboard.tsx", "./src/pages/suporte.tsx", "./src/pages/tailwindbreakpointtest.tsx", "./src/pages/zoomresponsivenesstest.tsx", "./src/pages/convite/aceitar.tsx", "./src/pages/estudante/[id]/familia.tsx", "./src/types/designacoes.ts", "./src/types/enhanced-estudantes.ts", "./src/types/estudantes.ts", "./src/types/family.ts", "./src/types/meetings.ts", "./src/types/spreadsheet.ts", "./src/types/supabase-extensions.ts", "./src/types/tutorial.ts", "./src/types/unified-system.ts", "./src/utils/addredirecturl.ts", "./src/utils/analyzedatabaseinconsistencies.ts", "./src/utils/assignmentgenerator.ts", "./src/utils/authdebounce.ts", "./src/utils/authflowtest.ts", "./src/utils/authlogger.ts", "./src/utils/authtimeoutdiagnostics.ts", "./src/utils/balanceamentohistorico.ts", "./src/utils/createcongregacoestable.ts", "./src/utils/dataloaders.ts", "./src/utils/datamigrationhelper.ts", "./src/utils/datevalidationtest.ts", "./src/utils/debuglogger.ts", "./src/utils/emergencylogout.ts", "./src/utils/enhancedfamilyvalidation.ts", "./src/utils/exceltemplategenerator.ts", "./src/utils/executedirectsql.ts", "./src/utils/executemigration.ts", "./src/utils/familyinferenceengine.ts", "./src/utils/familyinvitationdebug.ts", "./src/utils/familymemberdebug.ts", "./src/utils/fixallviamcp.ts", "./src/utils/fixcongregacaosystem.ts", "./src/utils/forcelogout.ts", "./src/utils/jworgcontentparser.ts", "./src/utils/logoutdiagnostics.ts", "./src/utils/pagerefreshoptimization.ts", "./src/utils/pdfgenerator.ts", "./src/utils/pdfparser.ts", "./src/utils/performancemonitor.ts", "./src/utils/phonevalidationtest.ts", "./src/utils/profilecache.ts", "./src/utils/quicksync.ts", "./src/utils/regionalconnectivitytest.ts", "./src/utils/regrass38t.ts", "./src/utils/reviewdatabase.ts", "./src/utils/runglobalmigration.ts", "./src/utils/runmigration.ts", "./src/utils/safemcpfix.ts", "./src/utils/spreadsheetprocessor.ts", "./src/utils/supabaseconnectiontest.ts", "./src/utils/supabasehealthcheck.ts", "./src/utils/supabasetimeoutconfig.ts", "./src/utils/syncdatabase.ts", "./src/utils/syncprofilestoestudantes.ts", "./src/utils/syncstudentstoinstructors.ts", "./src/utils/tailwindbreakpointverification.ts", "./src/utils/testdensitytoggle.ts", "./src/utils/testfamilyinvitationsystem.ts", "./src/utils/testessistemadesignacoes.ts", "./src/utils/tratamentoerros.ts", "./src/utils/typecompatibility.ts", "./src/utils/usemcpsupabase.ts", "./src/utils/validacaofamiliar.ts", "./src/utils/validacaoseguranca.ts", "./src/utils/zoomresponsivenessutils.ts", "./src/verification/auth-config.ts", "./src/verification/auth-verifier.ts", "./src/verification/backend-verifier.ts", "./src/verification/base-verifier.ts", "./src/verification/cli.ts", "./src/verification/controller.ts", "./src/verification/download-verifier.ts", "./src/verification/frontend-verifier.ts", "./src/verification/index.ts", "./src/verification/infrastructure-verifier.ts", "./src/verification/integration-tester.ts", "./src/verification/interfaces.ts", "./src/verification/rbac-tester.ts", "./src/verification/routing-tester.ts", "./src/verification/session-tester.ts", "./src/verification/test-download-verifier.js", "./src/verification/test-frontend-verifier.ts", "./src/verification/types.ts", "./src/verification/utils.ts"], "errors": true, "version": "5.9.2"}