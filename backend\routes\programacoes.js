const express = require('express');
const router = express.Router();
const { supabase } = require('../config/database');

// Simple auth stub (adjust to your auth system)
const requireAuth = (req, res, next) => {
  // In dev we accept requests without real auth; plug your JWT here
  next();
};

// Helpers
async function getProgramacaoByWeek(weekDate) {
  const { data, error } = await supabase
    .from('programacoes')
    .select('*')
    .lte('semana_data_inicio', weekDate)
    .gte('semana_data_fim', weekDate)
    .order('created_at', { ascending: false });
  if (error) throw error;
  return data || [];
}

async function getItens(programacao_id) {
  const { data, error } = await supabase
    .from('programacao_itens')
    .select('*')
    .eq('programacao_id', programacao_id)
    .order('ordem', { ascending: true });
  if (error) throw error;
  return data || [];
}

// Admin: cria rascunho (com ou sem itens)
router.post('/', requireAuth, async (req, res) => {
  try {
    const { semana_data_inicio, semana_data_fim, status = 'rascunho', itens = [], congregacao_id = null } = req.body || {};

    if (!semana_data_inicio || !semana_data_fim) {
      return res.status(400).json({ error: 'semana_data_inicio e semana_data_fim são obrigatórios' });
    }

    const { data: created, error } = await supabase
      .from('programacoes')
      .insert({ semana_data_inicio, semana_data_fim, status, congregacao_id })
      .select()
      .single();
    if (error) throw error;

    // Insert itens (no nomes, apenas estrutura)
    if (Array.isArray(itens) && itens.length > 0) {
      const rows = itens.map((i) => ({
        programacao_id: created.id,
        ordem: i.ordem,
        secao: i.secao,
        tipo: i.tipo,
        titulo: i.titulo,
        tema: i.tema ?? null,
        tempo_min: i.tempo_min,
        regras_papel: i.regras_papel ?? null,
      }));
      const { error: itensErr } = await supabase.from('programacao_itens').insert(rows);
      if (itensErr) throw itensErr;
    }

    const itensOut = await getItens(created.id);
    res.json({ success: true, programacao: { ...created, itens: itensOut } });
  } catch (err) {
    console.error('❌ POST /api/programacoes', err);
    res.status(500).json({ error: 'Falha ao criar programação', details: err.message });
  }
});

// Admin: publicar
router.put('/:id/publicar', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { data, error } = await supabase
      .from('programacoes')
      .update({ status: 'publicada' })
      .eq('id', id)
      .select()
      .single();
    if (error) throw error;
    const itens = await getItens(id);
    res.json({ success: true, programacao: { ...data, itens } });
  } catch (err) {
    console.error('❌ PUT /api/programacoes/:id/publicar', err);
    res.status(500).json({ error: 'Falha ao publicar programação', details: err.message });
  }
});

// Admin: editar estrutura/tempos/tema (sem nomes)
router.put('/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { semana_data_inicio, semana_data_fim, itens } = req.body || {};

    if (semana_data_inicio || semana_data_fim) {
      const upd = {};
      if (semana_data_inicio) upd.semana_data_inicio = semana_data_inicio;
      if (semana_data_fim) upd.semana_data_fim = semana_data_fim;
      const { error: updErr } = await supabase.from('programacoes').update(upd).eq('id', id);
      if (updErr) throw updErr;
    }

    if (Array.isArray(itens)) {
      // Replace items: delete then insert (simple and safe ordering)
      const { error: delErr } = await supabase.from('programacao_itens').delete().eq('programacao_id', id);
      if (delErr) throw delErr;
      if (itens.length > 0) {
        const rows = itens.map((i) => ({
          programacao_id: id,
          ordem: i.ordem,
          secao: i.secao,
          tipo: i.tipo,
          titulo: i.titulo,
          tema: i.tema ?? null,
          tempo_min: i.tempo_min,
          regras_papel: i.regras_papel ?? null,
        }));
        const { error: insErr } = await supabase.from('programacao_itens').insert(rows);
        if (insErr) throw insErr;
      }
    }

    const { data: out, error } = await supabase.from('programacoes').select('*').eq('id', id).single();
    if (error) throw error;
    const itensOut = await getItens(id);
    res.json({ success: true, programacao: { ...out, itens: itensOut } });
  } catch (err) {
    console.error('❌ PUT /api/programacoes/:id', err);
    res.status(500).json({ error: 'Falha ao atualizar programação', details: err.message });
  }
});

// Admin: obter por semana (com itens, sem nomes)
router.get('/', requireAuth, async (req, res) => {
  try {
    const { semana } = req.query;
    if (!semana) return res.status(400).json({ error: 'Parâmetro semana=YYYY-MM-DD é obrigatório' });
    const list = await getProgramacaoByWeek(semana);
    const withItems = await Promise.all(list.map(async (p) => ({ ...p, itens: await getItens(p.id) })));
    res.json({ success: true, results: withItems });
  } catch (err) {
    console.error('❌ GET /api/programacoes', err);
    res.status(500).json({ error: 'Falha ao obter programação', details: err.message });
  }
});

// Instrutor: publicadas por semana (com itens)
router.get('/publicadas', requireAuth, async (req, res) => {
  try {
    const { semana } = req.query;
    if (!semana) return res.status(400).json({ error: 'Parâmetro semana=YYYY-MM-DD é obrigatório' });
    const { data, error } = await supabase
      .from('programacoes')
      .select('*')
      .eq('status', 'publicada')
      .lte('semana_data_inicio', semana)
      .gte('semana_data_fim', semana)
      .order('created_at', { ascending: false });
    if (error) throw error;
    const withItems = await Promise.all((data || []).map(async (p) => ({ ...p, itens: await getItens(p.id) })));
    res.json({ success: true, results: withItems });
  } catch (err) {
    console.error('❌ GET /api/programacoes/publicadas', err);
    res.status(500).json({ error: 'Falha ao obter publicadas', details: err.message });
  }
});

module.exports = router;

