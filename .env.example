# =====================================================
# Sistema Ministerial - Environment Configuration
# =====================================================
# 
# IMPORTANT SECURITY NOTES:
# - Copy this file to .env and fill in your actual values
# - Never commit .env files to version control
# - Keep all tokens and credentials secure
# - Use different values for development, staging, and production
#
# =====================================================

# =====================================================
# DATABASE & BACKEND CONFIGURATION
# =====================================================

# Supabase Project Configuration
# Get these values from: https://supabase.com/dashboard/project/[project-id]/settings/api
VITE_SUPABASE_URL="https://your-project-id.supabase.co"
VITE_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your-anon-key-here"

# Supabase Database Connection (Server-side operations)
# Format: postgresql://postgres:[password]@db.[project-id].supabase.co:5432/postgres
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"

# Supabase Service Role Key (Server-side admin operations)
# WARNING: This key has admin privileges - keep it secure!
SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your-service-role-key-here"

# Supabase Personal Access Token (MCP Integration)
# Generate at: https://supabase.com/dashboard/account/tokens
SUPABASE_ACCESS_TOKEN="sbp_your-personal-access-token-here"

# =====================================================
# DEVELOPMENT TOOLS
# =====================================================

# GitHub Configuration
# Generate at: https://github.com/settings/tokens
# Required scopes: repo, workflow, read:org
GITHUB_TOKEN="ghp_your-github-personal-access-token-here"
GITHUB_REPOSITORY="your-username/sistema-ministerial"

# Development Environment
NODE_ENV="development"
VITE_APP_ENV="development"

# Application URLs
VITE_APP_URL="http://localhost:5173"
VITE_API_URL="http://localhost:5173/api"

# =====================================================
# TESTING INFRASTRUCTURE
# =====================================================

# Cypress Configuration
# Get from: https://cloud.cypress.io/projects/[project-id]/settings
CYPRESS_RECORD_KEY="your-cypress-record-key-here"
CYPRESS_PROJECT_ID="your-cypress-project-id"

# Test Environment URLs
CYPRESS_BASE_URL="http://localhost:5173"
CYPRESS_API_URL="http://localhost:5173/api"

# =====================================================
# TEST USER CREDENTIALS
# =====================================================

# Instructor Test Account (Full Administrative Access)
# Role: 'instrutor' - Can manage students, create programs, assign tasks
TEST_INSTRUCTOR_EMAIL="<EMAIL>"
TEST_INSTRUCTOR_PASSWORD="TestInstrutor123!"
TEST_INSTRUCTOR_NAME="Instrutor de Teste"
TEST_INSTRUCTOR_CONGREGATION="Congregação Teste"

# Student Test Account (Limited Portal Access)
# Role: 'estudante' - Can view assignments, submit reports
TEST_STUDENT_EMAIL="<EMAIL>"
TEST_STUDENT_PASSWORD="TestEstudante123!"
TEST_STUDENT_NAME="Estudante de Teste"
TEST_STUDENT_CONGREGATION="Congregação Teste"

# Developer Test Account (Developer Panel Access)
# Role: 'developer' - Can access developer panel, manage templates
TEST_DEVELOPER_EMAIL="<EMAIL>"
TEST_DEVELOPER_PASSWORD="TestDeveloper123!"
TEST_DEVELOPER_NAME="Desenvolvedor de Teste"
TEST_DEVELOPER_CONGREGATION="Congregação Teste"

# Legacy Franklin Account (Backward Compatibility)
# For existing test commands and legacy functionality
FRANKLIN_EMAIL="<EMAIL>"
FRANKLIN_PASSWORD="Franklin123!"
FRANKLIN_NAME="Franklin Teste"

# =====================================================
# SECURITY & AUTHENTICATION
# =====================================================

# JWT Configuration
JWT_SECRET="your-super-secure-jwt-secret-key-here-min-32-chars"
JWT_EXPIRES_IN="7d"

# Session Configuration
SESSION_SECRET="your-session-secret-key-here-min-32-chars"
SESSION_TIMEOUT="3600000"  # 1 hour in milliseconds

# Password Requirements
MIN_PASSWORD_LENGTH="8"
REQUIRE_SPECIAL_CHARS="true"
REQUIRE_NUMBERS="true"

# =====================================================
# FILE STORAGE & UPLOADS
# =====================================================

# Supabase Storage Configuration
VITE_SUPABASE_STORAGE_URL="https://your-project-id.supabase.co/storage/v1"
SUPABASE_STORAGE_BUCKET="sistema-ministerial"

# File Upload Limits
MAX_FILE_SIZE="********"  # 10MB in bytes
ALLOWED_FILE_TYPES="application/pdf,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv"

# Template Storage
TEMPLATE_STORAGE_PATH="templates"
UPLOAD_STORAGE_PATH="uploads"

# =====================================================
# EMAIL CONFIGURATION (Optional)
# =====================================================

# SMTP Configuration for notifications
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Email Templates
FROM_EMAIL="<EMAIL>"
FROM_NAME="Sistema Ministerial"

# =====================================================
# LOGGING & MONITORING
# =====================================================

# Log Level (error, warn, info, debug)
LOG_LEVEL="info"

# Sentry Configuration (Error Monitoring)
VITE_SENTRY_DSN="https://<EMAIL>/project-id"
SENTRY_AUTH_TOKEN="your-sentry-auth-token"

# Analytics (Optional)
VITE_GOOGLE_ANALYTICS_ID="GA-XXXXXXXXX"

# =====================================================
# FEATURE FLAGS
# =====================================================

# Developer Panel Features
VITE_ENABLE_DEVELOPER_PANEL="true"
VITE_ENABLE_TEMPLATE_LIBRARY="true"
VITE_ENABLE_ADVANCED_TUTORIALS="true"

# Experimental Features
VITE_ENABLE_OFFLINE_MODE="false"
VITE_ENABLE_PWA="true"
VITE_ENABLE_DARK_MODE="true"

# Debug Features (Development Only)
VITE_ENABLE_DEBUG_PANEL="true"
VITE_SHOW_PERFORMANCE_METRICS="true"
VITE_ENABLE_MOCK_DATA="false"

# =====================================================
# THIRD-PARTY INTEGRATIONS
# =====================================================

# JW.org Integration (if applicable)
JW_ORG_API_KEY="your-jw-org-api-key-if-available"

# Microsoft Graph (for Excel integration)
MICROSOFT_CLIENT_ID="your-microsoft-app-client-id"
MICROSOFT_CLIENT_SECRET="your-microsoft-app-client-secret"

# =====================================================
# PERFORMANCE & CACHING
# =====================================================

# Cache Configuration
CACHE_TTL="3600"  # 1 hour in seconds
ENABLE_REDIS_CACHE="false"
REDIS_URL="redis://localhost:6379"

# CDN Configuration (Production)
CDN_URL="https://cdn.exemplo.com"
STATIC_ASSETS_URL="https://assets.exemplo.com"

# =====================================================
# ENVIRONMENT-SPECIFIC OVERRIDES
# =====================================================

# Development Specific
VITE_DEV_TOOLS="true"
VITE_HOT_RELOAD="true"

# Production Specific (uncomment for production)
# VITE_APP_ENV="production"
# NODE_ENV="production"
# VITE_DEV_TOOLS="false"
# LOG_LEVEL="error"

# Staging Specific (uncomment for staging)
# VITE_APP_ENV="staging"
# NODE_ENV="staging"
# LOG_LEVEL="warn"
