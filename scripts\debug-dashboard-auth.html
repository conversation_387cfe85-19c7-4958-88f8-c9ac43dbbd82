<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Authentication Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 Dashboard Authentication Debug Tool</h1>
    
    <div class="debug-section">
        <h2>Current Authentication Status</h2>
        <div id="auth-status">Loading...</div>
        <button onclick="checkAuthStatus()">Refresh Auth Status</button>
        <button onclick="clearStorage()">Clear Browser Storage</button>
    </div>

    <div class="debug-section">
        <h2>Test Login</h2>
        <div>
            <label>Email: <input type="email" id="email" placeholder="<EMAIL>"></label><br><br>
            <label>Password: <input type="password" id="password" placeholder="password"></label><br><br>
            <button onclick="testLogin()">Test Login</button>
            <button onclick="testLogout()">Test Logout</button>
        </div>
        <div id="login-status"></div>
    </div>

    <div class="debug-section">
        <h2>Navigation Tests</h2>
        <button onclick="goToDashboard()">Go to Dashboard</button>
        <button onclick="goToAuth()">Go to Auth Page</button>
        <button onclick="goToHome()">Go to Home</button>
        <div id="navigation-status"></div>
    </div>

    <div class="debug-section">
        <h2>Debug Logs</h2>
        <button onclick="clearLogs()">Clear Logs</button>
        <pre id="debug-logs"></pre>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js';

        const SUPABASE_URL = 'https://nwpuurgwnnuejqinkvrh.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im53cHV1cmd3bm51ZWpxaW5rdnJoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzI5NzQsImV4cCI6MjA1MDU0ODk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        let logs = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const logElement = document.getElementById('debug-logs');
            logElement.textContent = logs.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logEntry);
        }

        window.clearLogs = function() {
            logs = [];
            document.getElementById('debug-logs').textContent = '';
        }

        window.checkAuthStatus = async function() {
            log('🔍 Checking authentication status...');
            
            try {
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (error) {
                    log(`❌ Session error: ${error.message}`, 'error');
                    document.getElementById('auth-status').innerHTML = 
                        `<div class="status error">❌ Session Error: ${error.message}</div>`;
                    return;
                }

                if (session) {
                    log(`✅ User is logged in: ${session.user.email}`);
                    log(`   User ID: ${session.user.id}`);
                    log(`   Role (metadata): ${session.user.user_metadata?.role || 'Not set'}`);
                    log(`   Session expires: ${new Date(session.expires_at * 1000).toLocaleString()}`);
                    
                    // Try to get profile
                    try {
                        const { data: profile, error: profileError } = await supabase
                            .from('user_profiles')
                            .select('*')
                            .eq('id', session.user.id)
                            .single();
                        
                        if (profileError) {
                            log(`⚠️ Profile error: ${profileError.message}`);
                        } else {
                            log(`📋 Profile loaded: ${profile.nome_completo} (${profile.role})`);
                        }
                    } catch (e) {
                        log(`⚠️ Profile fetch failed: ${e.message}`);
                    }
                    
                    document.getElementById('auth-status').innerHTML = 
                        `<div class="status success">✅ Logged in as: ${session.user.email}<br>
                         Role: ${session.user.user_metadata?.role || 'Not set'}<br>
                         User ID: ${session.user.id}</div>`;
                } else {
                    log('❌ No active session - user is not logged in');
                    document.getElementById('auth-status').innerHTML = 
                        `<div class="status error">❌ Not logged in</div>`;
                }
            } catch (error) {
                log(`❌ Auth check failed: ${error.message}`, 'error');
                document.getElementById('auth-status').innerHTML = 
                    `<div class="status error">❌ Auth check failed: ${error.message}</div>`;
            }
        }

        window.testLogin = async function() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                document.getElementById('login-status').innerHTML = 
                    `<div class="status warning">⚠️ Please enter email and password</div>`;
                return;
            }
            
            log(`🔑 Attempting login with: ${email}`);
            
            try {
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });
                
                if (error) {
                    log(`❌ Login failed: ${error.message}`, 'error');
                    document.getElementById('login-status').innerHTML = 
                        `<div class="status error">❌ Login failed: ${error.message}</div>`;
                } else {
                    log(`✅ Login successful: ${data.user.email}`);
                    document.getElementById('login-status').innerHTML = 
                        `<div class="status success">✅ Login successful!</div>`;
                    
                    // Refresh auth status
                    setTimeout(checkAuthStatus, 1000);
                }
            } catch (error) {
                log(`❌ Login exception: ${error.message}`, 'error');
                document.getElementById('login-status').innerHTML = 
                    `<div class="status error">❌ Login exception: ${error.message}</div>`;
            }
        }

        window.testLogout = async function() {
            log('🚪 Attempting logout...');
            
            try {
                const { error } = await supabase.auth.signOut();
                
                if (error) {
                    log(`❌ Logout failed: ${error.message}`, 'error');
                } else {
                    log('✅ Logout successful');
                    document.getElementById('login-status').innerHTML = 
                        `<div class="status success">✅ Logout successful!</div>`;
                    
                    // Refresh auth status
                    setTimeout(checkAuthStatus, 1000);
                }
            } catch (error) {
                log(`❌ Logout exception: ${error.message}`, 'error');
            }
        }

        window.clearStorage = function() {
            log('🧹 Clearing browser storage...');
            localStorage.clear();
            sessionStorage.clear();
            log('✅ Browser storage cleared');
            
            // Refresh auth status
            setTimeout(checkAuthStatus, 1000);
        }

        window.goToDashboard = function() {
            log('🎯 Navigating to dashboard...');
            window.location.href = 'http://localhost:5173/dashboard';
        }

        window.goToAuth = function() {
            log('🔑 Navigating to auth page...');
            window.location.href = 'http://localhost:5173/auth';
        }

        window.goToHome = function() {
            log('🏠 Navigating to home page...');
            window.location.href = 'http://localhost:5173/';
        }

        // Initial auth check
        checkAuthStatus();
    </script>
</body>
</html>
