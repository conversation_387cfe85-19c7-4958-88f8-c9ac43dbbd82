<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Blank Screen Fix - Sistema Ministerial</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffe6e6; border-left: 4px solid #ff0000; }
        .success { background: #e6ffe6; border-left: 4px solid #00aa00; }
        .info { background: #e6f3ff; border-left: 4px solid #0066cc; }
        .warning { background: #fff3cd; border-left: 4px solid #ffc107; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .test-section { border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .status { font-weight: bold; padding: 5px 10px; border-radius: 3px; }
        .status.fixed { background: #d4edda; color: #155724; }
        .status.broken { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🔧 Test Blank Screen Fix - Sistema Ministerial</h1>
    <p>This tool verifies that the React useEffect error has been fixed.</p>
    
    <div class="test-section">
        <h3>🚨 Error Status</h3>
        <div id="errorStatus" class="status broken">Checking...</div>
        <p id="errorDescription">Checking for React useEffect errors...</p>
    </div>
    
    <div class="test-section">
        <h3>📋 What Was Fixed</h3>
        <ul>
            <li><strong>useEffect Return Error</strong>: Fixed JSX returns inside useEffect hooks</li>
            <li><strong>destroy is not a function</strong>: Removed invalid cleanup functions</li>
            <li><strong>Blank Screen Issue</strong>: Moved rendering logic outside useEffect</li>
            <li><strong>Loading States</strong>: Proper loading components now render correctly</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h3>🧪 Test Instructions</h3>
        <ol>
            <li><strong>Check Console</strong>: Open browser DevTools (F12) and check Console tab</li>
            <li><strong>Look for Errors</strong>: Should see NO "destroy is not a function" errors</li>
            <li><strong>Test Navigation</strong>: Try navigating to the student portal URL</li>
            <li><strong>Verify Loading</strong>: Should see loading spinners, not blank screens</li>
        </ol>
        
        <div>
            <button onclick="testConsoleErrors()">Check Console Errors</button>
            <button onclick="testNavigation()">Test Navigation</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>
    </div>
    
    <div class="test-section">
        <h3>🔗 Quick Links</h3>
        <div>
            <button onclick="goToAuth()">Go to Auth Page</button>
            <button onclick="goToFranklinPortal()">Go to Franklin's Portal</button>
            <button onclick="goToHome()">Go to Home</button>
        </div>
    </div>
    
    <div id="logs"></div>

    <script>
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logsDiv.appendChild(logDiv);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        function updateErrorStatus(isFixed, description) {
            const statusDiv = document.getElementById('errorStatus');
            const descDiv = document.getElementById('errorDescription');
            
            if (isFixed) {
                statusDiv.className = 'status fixed';
                statusDiv.textContent = '✅ FIXED';
            } else {
                statusDiv.className = 'status broken';
                statusDiv.textContent = '❌ STILL BROKEN';
            }
            
            descDiv.textContent = description;
        }
        
        function testConsoleErrors() {
            clearLogs();
            log('🔍 Checking for console errors...', 'info');
            
            // Check if there are any React errors in console
            const originalError = console.error;
            let reactErrors = [];
            
            console.error = function(...args) {
                const message = args.join(' ');
                if (message.includes('useEffect') || 
                    message.includes('destroy is not a function') ||
                    message.includes('ProtectedRoute')) {
                    reactErrors.push(message);
                }
                originalError.apply(console, args);
            };
            
            // Restore original console.error after a short delay
            setTimeout(() => {
                console.error = originalError;
                
                if (reactErrors.length === 0) {
                    log('✅ No React useEffect errors found!', 'success');
                    updateErrorStatus(true, 'No useEffect errors detected in console.');
                } else {
                    log(`❌ Found ${reactErrors.length} React errors:`, 'error');
                    reactErrors.forEach(error => {
                        log(`   ${error}`, 'error');
                    });
                    updateErrorStatus(false, `Found ${reactErrors.length} React useEffect errors.`);
                }
            }, 2000);
            
            log('⏳ Monitoring console for 2 seconds...', 'info');
        }
        
        function testNavigation() {
            clearLogs();
            log('🧭 Testing navigation to student portal...', 'info');
            
            const franklinPortalUrl = 'http://localhost:5173/estudante/77c99e53-500b-4140-b7fc-a69f96b216e1';
            
            log(`📍 Navigating to: ${franklinPortalUrl}`, 'info');
            log('⚠️ Watch for:', 'warning');
            log('   - Loading spinners (good)', 'info');
            log('   - Blank white screen (bad)', 'warning');
            log('   - Console errors (bad)', 'warning');
            
            // Open in new tab to avoid losing this test page
            window.open(franklinPortalUrl, '_blank');
            
            log('✅ Navigation test initiated. Check the new tab!', 'success');
        }
        
        function goToAuth() {
            window.open('http://localhost:5173/auth', '_blank');
            log('📍 Opened auth page in new tab', 'info');
        }
        
        function goToFranklinPortal() {
            window.open('http://localhost:5173/estudante/77c99e53-500b-4140-b7fc-a69f96b216e1', '_blank');
            log('📍 Opened Franklin\'s portal in new tab', 'info');
        }
        
        function goToHome() {
            window.open('http://localhost:5173/', '_blank');
            log('📍 Opened home page in new tab', 'info');
        }
        
        // Auto-check for errors on page load
        setTimeout(() => {
            testConsoleErrors();
        }, 1000);
        
        // Initial log
        log('🔧 Blank screen fix test tool loaded.', 'info');
        log('📋 The fix removes JSX returns from useEffect hooks.', 'info');
        log('🎯 Expected result: No more "destroy is not a function" errors.', 'info');
        
        // Monitor for any immediate errors
        window.addEventListener('error', (event) => {
            if (event.message.includes('destroy is not a function') || 
                event.message.includes('useEffect')) {
                log(`❌ Detected error: ${event.message}`, 'error');
                updateErrorStatus(false, 'Runtime error detected: ' + event.message);
            }
        });
        
        // Check if we're running on localhost
        if (window.location.hostname !== 'localhost') {
            log('⚠️ This test is designed for localhost:5173', 'warning');
            log('   Update the URLs if running on a different server', 'warning');
        }
    </script>
</body>
</html>
