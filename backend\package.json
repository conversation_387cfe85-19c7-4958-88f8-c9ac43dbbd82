{"name": "sistema-ministerial-backend", "version": "1.0.0", "description": "Backend para download automático de materiais JW.org e geração de programas", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "backup": "node scripts/backup.js", "cleanup": "node scripts/cleanup.js"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "fs-extra": "^11.1.1", "helmet": "^7.1.0", "node-cron": "^3.0.3", "node-fetch": "^2.7.0", "path": "^0.12.7"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}, "keywords": ["jw.org", "download", "automático", "ministerial", "programas", "congregações"], "author": "<PERSON>", "license": "MIT", "engines": {"node": ">=18.0.0"}}